[15:10:01] [main/INFO] (FabricLoader/GameProvider) Loading Minecraft 1.21.8 with Fabric Loader 0.16.14
[15:10:02] [main/INFO] (FabricLoader) Loading 49 mods:
	- ai_villagers 1.0.0
	- fabric-api 0.129.0+1.21.8
	- fabric-api-base 0.4.64+9ec45cd8f3
	- fabric-api-lookup-api-v1 1.6.100+946bf4c3f3
	- fabric-biome-api-v1 16.0.11+946bf4c3f3
	- fabric-block-api-v1 1.1.3+946bf4c3f3
	- fabric-block-view-api-v2 1.0.31+946bf4c3f3
	- fabric-client-gametest-api-v1 4.2.5+8a98c3fcf3
	- fabric-command-api-v2 2.2.53+946bf4c3f3
	- fabric-content-registries-v0 10.0.18+946bf4c3f3
	- fabric-convention-tags-v1 2.1.40+7f945d5bf3
	- fabric-convention-tags-v2 2.15.5+eb5df52ff3
	- fabric-crash-report-info-v1 0.3.15+946bf4c3f3
	- fabric-data-attachment-api-v1 1.8.10+946bf4c3f3
	- fabric-data-generation-api-v1 23.2.4+55e55d29f3
	- fabric-dimensions-v1 4.0.19+946bf4c3f3
	- fabric-entity-events-v1 2.1.1+c9e47273f3
	- fabric-events-interaction-v0 4.0.23+946bf4c3f3
	- fabric-game-rule-api-v1 1.0.73+c64c9c5bf3
	- fabric-gametest-api-v1 3.1.9+39ce47f5f3
	- fabric-item-api-v1 11.4.3+946bf4c3f3
	- fabric-item-group-api-v1 4.2.13+946bf4c3f3
	- fabric-key-binding-api-v1 1.0.65+946bf4c3f3
	- fabric-lifecycle-events-v1 2.6.3+db4dfd85f3
	- fabric-loot-api-v2 3.0.55+3f89f5a5f3
	- fabric-loot-api-v3 2.0.2+946bf4c3f3
	- fabric-message-api-v1 6.1.1+946bf4c3f3
	- fabric-model-loading-api-v1 5.2.5+946bf4c3f3
	- fabric-networking-api-v1 5.0.1+946bf4c3f3
	- fabric-object-builder-api-v1 21.1.7+946bf4c3f3
	- fabric-particles-v1 4.1.7+946bf4c3f3
	- fabric-recipe-api-v1 8.1.14+946bf4c3f3
	- fabric-registry-sync-v0 6.1.27+946bf4c3f3
	- fabric-renderer-api-v1 7.0.2+946bf4c3f3
	- fabric-renderer-indigo 4.0.2+946bf4c3f3
	- fabric-rendering-fluids-v1 3.1.30+fa6cb72bf3
	- fabric-rendering-v1 12.4.0+e8d43c76f3
	- fabric-resource-conditions-api-v1 5.0.24+946bf4c3f3
	- fabric-resource-loader-v0 3.1.11+946bf4c3f3
	- fabric-screen-api-v1 2.1.0+277ecf7df3
	- fabric-screen-handler-api-v1 1.3.136+946bf4c3f3
	- fabric-sound-api-v1 1.0.42+946bf4c3f3
	- fabric-tag-api-v1 1.2.1+946bf4c3f3
	- fabric-transfer-api-v1 6.0.5+946bf4c3f3
	- fabric-transitive-access-wideners-v1 6.4.1+ac3e15d1f3
	- fabricloader 0.16.14
	- java 23
	- minecraft 1.21.8
	- mixinextras 0.4.1
[15:10:02] [main/INFO] (FabricLoader/Mixin) SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.5+mixin.0.8.7/22f9eb729e216a091673a574a5906dc1b9027fb3/sponge-mixin-0.15.5+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[15:10:03] [main/INFO] (FabricLoader/Mixin) Loaded Fabric development mappings for mixin remapper!
[15:10:03] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_21
[15:10:03] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_22
[15:10:07] [main/INFO] (FabricLoader/MixinExtras|Service) Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[15:10:11] [Datafixer Bootstrap/INFO] (Minecraft) 269 Datafixer optimizations took 1934 milliseconds
[15:10:39] [Render thread/INFO] (Minecraft) Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[15:10:39] [Render thread/INFO] (Minecraft) Setting user: Player441
[15:10:39] [Render thread/INFO] (ai_villagers) Inicializando ai_villagers!
[15:10:39] [Render thread/INFO] (ai_villagers) Items personalizados de AiVillagers registrados
[15:10:39] [Render thread/INFO] (ai_villagers) Entidades personalizadas de AiVillagers registradas
[15:10:40] [Render thread/INFO] (ai_villagers) Inicializando cliente de AiVillagers
[15:10:40] [Render thread/INFO] (ai_villagers) Cliente de AiVillagers inicializado correctamente
[15:10:40] [Render thread/INFO] (Indigo) [Indigo] Registering Indigo renderer!
[15:10:41] [Render thread/INFO] (Minecraft) Backend library: LWJGL version 3.3.3-snapshot
[15:10:43] [Render thread/INFO] (Minecraft) Using optional rendering extensions: GL_ARB_buffer_storage, GL_KHR_debug, GL_ARB_vertex_attrib_binding, GL_ARB_direct_state_access
[15:10:49] [Render thread/INFO] (Minecraft) Reloading ResourceManager: vanilla, fabric, ai_villagers, fabric-api, fabric-api-base, fabric-api-lookup-api-v1, fabric-biome-api-v1, fabric-block-api-v1, fabric-block-view-api-v2, fabric-client-gametest-api-v1, fabric-command-api-v2, fabric-content-registries-v0, fabric-convention-tags-v1, fabric-convention-tags-v2, fabric-crash-report-info-v1, fabric-data-attachment-api-v1, fabric-data-generation-api-v1, fabric-dimensions-v1, fabric-entity-events-v1, fabric-events-interaction-v0, fabric-game-rule-api-v1, fabric-gametest-api-v1, fabric-item-api-v1, fabric-item-group-api-v1, fabric-key-binding-api-v1, fabric-lifecycle-events-v1, fabric-loot-api-v2, fabric-loot-api-v3, fabric-message-api-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-object-builder-api-v1, fabric-particles-v1, fabric-recipe-api-v1, fabric-registry-sync-v0, fabric-renderer-api-v1, fabric-renderer-indigo, fabric-rendering-fluids-v1, fabric-rendering-v1, fabric-resource-conditions-api-v1, fabric-resource-loader-v0, fabric-screen-api-v1, fabric-screen-handler-api-v1, fabric-sound-api-v1, fabric-tag-api-v1, fabric-transfer-api-v1, fabric-transitive-access-wideners-v1, fabricloader
[15:10:49] [Worker-Main-3/INFO] (Minecraft) Found unifont_pua-16.0.03.hex, loading
[15:10:49] [Worker-Main-6/INFO] (Minecraft) Found unifont_all_no_pua-16.0.03.hex, loading
[15:10:50] [Worker-Main-7/INFO] (Minecraft) Found unifont_jp_patch-16.0.03.hex, loading
[15:10:53] [Render thread/INFO] (Minecraft) OpenAL initialized on device OpenAL Soft on Altavoces (2- Realtek(R) Audio)
[15:10:53] [Render thread/INFO] (Minecraft) Sound engine started
[15:10:54] [Render thread/INFO] (Minecraft) Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[15:10:55] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[15:10:55] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[15:10:55] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[15:10:55] [Render thread/INFO] (Minecraft) Created: 2048x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[15:10:56] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[15:10:56] [Render thread/INFO] (Minecraft) Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[15:10:56] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[15:10:56] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[15:10:56] [Render thread/INFO] (Minecraft) Created: 64x64x0 minecraft:textures/atlas/map_decorations.png-atlas
[15:10:57] [Render thread/INFO] (Minecraft) Created: 512x256x0 minecraft:textures/atlas/particles.png-atlas
[15:10:57] [Render thread/INFO] (Minecraft) Created: 512x256x0 minecraft:textures/atlas/paintings.png-atlas
[15:10:57] [Render thread/INFO] (Minecraft) Created: 1024x512x0 minecraft:textures/atlas/gui.png-atlas
[15:11:50] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 2 reached during a single frame. New capacity will be 4.
[15:11:54] [Render thread/INFO] (Minecraft) Loaded 1407 recipes
[15:11:54] [Render thread/INFO] (Minecraft) Loaded 1520 advancements
[15:11:54] [Render thread/INFO] (BiomeModificationImpl) Applied 0 biome modifications to 0 of 65 new biomes in 2.691 ms
[15:11:54] [Server thread/INFO] (Minecraft) Starting integrated minecraft server version 1.21.8
[15:11:54] [Server thread/INFO] (Minecraft) Generating keypair
[15:11:55] [Server thread/INFO] (Minecraft) Preparing start region for dimension minecraft:overworld
[15:11:56] [Render thread/INFO] (Minecraft) Preparando zona de aparición: 0 %
[15:11:56] [Render thread/INFO] (Minecraft) Preparando zona de aparición: 0 %
[15:11:56] [Render thread/INFO] (Minecraft) Time elapsed: 612 ms
[15:11:56] [Server thread/INFO] (Minecraft) Changing view distance to 4, from 10
[15:11:56] [Server thread/INFO] (Minecraft) Changing simulation distance to 10, from 0
[15:11:58] [Server thread/INFO] (Minecraft) Player441[local:E:5018e8a1] logged in with entity id 17 at (0.29841873851293865, -60.0, -3.7353002432351277)
[15:11:59] [Server thread/INFO] (Minecraft) Player441 se ha unido a la partida
[15:11:59] [Server thread/INFO] (Minecraft) Player441 ha conseguido el progreso [La Edad de Hierro]
[15:11:59] [Render thread/INFO] (Minecraft) [System] [CHAT] Player441 ha conseguido el progreso [La Edad de Hierro]
[15:11:59] [Render thread/INFO] (Minecraft) Loaded 122 advancements
[15:11:59] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 4 reached during a single frame. New capacity will be 16.
[15:12:00] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 16 reached during a single frame. New capacity will be 32.
[15:12:00] [Server thread/INFO] (Minecraft) Saving and pausing game...
[15:12:00] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 32 reached during a single frame. New capacity will be 64.
[15:12:00] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:overworld
[15:12:00] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_nether
[15:12:00] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_end
[15:12:00] [Render thread/INFO] (Minecraft) Resizing Dynamic Transforms UBO, capacity limit of 64 reached during a single frame. New capacity will be 128.
[15:12:15] [Server thread/INFO] (Minecraft) [Player441: Tiempo ajustado a 1000]
[15:12:15] [Render thread/INFO] (Minecraft) [System] [CHAT] Tiempo ajustado a 1000
[15:12:19] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:12:19] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=41, y=-60, z=-4} en radios 1-16 (adaptativo hasta 24)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 7: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 9: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 11: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 13: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio 15: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Poca agua encontrada (0), extendiendo búsqueda hasta radio 24
[15:12:19] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio extendido 20: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio extendido 22: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Radio extendido 24: encontrados 0 bloques de agua superficial (total: 0)
[15:12:19] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 0
[15:12:19] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:12:19] [Server thread/INFO] (ai_villagers) No se encontraron spots fallidos, buscando cualquier spot ignorando reservas...
[15:12:19] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 26,0s, experiencia: 26,0s, variación: +6,0s]
[15:12:21] [Server thread/INFO] (Minecraft) [Player441: Se ha cambiado la vel. de tics objetivo a 60.0 por segundo]
[15:12:21] [Render thread/INFO] (Minecraft) [System] [CHAT] Se ha cambiado la vel. de tics objetivo a 60.0 por segundo
[15:12:29] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:12:29] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=40, y=-60, z=-5} en radios 1-16 (adaptativo hasta 24)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 7: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 9: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 11: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 13: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio 15: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Poca agua encontrada (0), extendiendo búsqueda hasta radio 24
[15:12:29] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio extendido 20: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio extendido 22: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Radio extendido 24: encontrados 0 bloques de agua superficial (total: 0)
[15:12:29] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 0
[15:12:29] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:12:29] [Server thread/INFO] (ai_villagers) No se encontraron spots fallidos, buscando cualquier spot ignorando reservas...
[15:12:29] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 26,0s, experiencia: 31,0s, variación: +1,0s]
[15:12:31] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:12:31] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=40, y=-60, z=-5} en radios 1-16 (adaptativo hasta 24)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 7: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 9: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 11: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 13: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio 15: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Poca agua encontrada (0), extendiendo búsqueda hasta radio 24
[15:12:31] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio extendido 20: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio extendido 22: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Radio extendido 24: encontrados 0 bloques de agua superficial (total: 0)
[15:12:31] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 0
[15:12:31] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:12:31] [Server thread/INFO] (ai_villagers) No se encontraron spots fallidos, buscando cualquier spot ignorando reservas...
[15:12:31] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 32,0s, experiencia: 32,0s, variación: +0,0s]
[15:12:32] [Server thread/INFO] (Minecraft) [Player441: Se ha cambiado el tiempo a despejado]
[15:12:32] [Render thread/INFO] (Minecraft) [System] [CHAT] Se ha cambiado el tiempo a despejado
[15:12:39] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=14, y=-60, z=-4} en radios 1-16 (adaptativo hasta 24)
[15:12:39] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:12:39] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:12:39] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 0 bloques de agua superficial (total: 0)
[15:12:39] [Server thread/INFO] (ai_villagers) Radio 7: encontrados 0 bloques de agua superficial (total: 0)
[15:12:39] [Server thread/INFO] (ai_villagers) Radio 9: encontrados 0 bloques de agua superficial (total: 0)
[15:12:39] [Server thread/INFO] (ai_villagers) Radio 11: encontrados 0 bloques de agua superficial (total: 0)
[15:12:39] [Server thread/INFO] (ai_villagers) Radio 13: encontrados 3 bloques de agua superficial (total: 3)
[15:12:39] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:12:39] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:12:39] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:12:39] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:12:39] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=1, y=-61, z=-1}
[15:12:39] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=1, y=-61, z=0}
[15:12:39] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=0, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:12:39] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:39] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:39] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:12:40] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:12:40] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:12:40] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:12:40] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=1, y=-60, z=-3} a nivel de agua: -60
[15:12:40] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=1, y=-60, z=-3} (se liberará automáticamente en 20 segundos)
[15:12:40] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=1, y=-60, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=1, y=-61, z=-2} - muy cerca (distancia: 1.7320508075688772)
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (1,50, -60,90, -1,50) desde posición: (2,03, -60,00, -2,93)
[15:12:41] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=1, y=-61, z=-2} (resultado: FALLO, evento: 13.6s)
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=1, y=-60, z=-3} hacia agua: Mutable{x=1, y=-61, z=-2} (resultado: FALLO, evento en: 13.6s)
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=1, y=-60, z=-3} y comenzó a pescar inmediatamente
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:12:41] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-2, y=-60, z=-4} en radios 1-16 (adaptativo hasta 24)
[15:12:41] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:12:41] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 6 bloques de agua superficial (total: 6)
[15:12:41] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 6
[15:12:41] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-1, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-1, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=0, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=0, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=1, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:12:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:12:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:12:41] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:12:41] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=-1, y=-60, z=-3} a nivel de agua: -60
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-1, y=-60, z=-3} (se liberará automáticamente en 20 segundos)
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-1, y=-60, z=-3}
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=-2} - muy cerca (distancia: 1.7320508075688772)
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, -1,50) desde posición: (-1,44, -60,00, -2,94)
[15:12:41] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=-2} (resultado: FALLO, evento: 14.6s)
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-1, y=-60, z=-3} hacia agua: Mutable{x=-1, y=-61, z=-2} (resultado: FALLO, evento en: 14.6s)
[15:12:41] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-1, y=-60, z=-3} y comenzó a pescar inmediatamente
[15:12:46] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca FALLIDA en 13.6s
[15:12:46] [Server thread/INFO] (Minecraft) [Player441: Se ha cambiado la vel. de tics objetivo a 40.0 por segundo]
[15:12:46] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca FALLIDA en 14.6s
[15:12:46] [Render thread/INFO] (Minecraft) [System] [CHAT] Se ha cambiado la vel. de tics objetivo a 40.0 por segundo
[15:12:48] [Server thread/INFO] (ai_villagers) Pescador Pescador FALLÓ pescando en BlockPos{x=1, y=-60, z=-3} (fallos consecutivos: 4, éxitos acumulados: 1, tiempo total: 18.6s)
[15:12:48] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=1, y=-60, z=-3}
[15:12:48] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 26,0s, experiencia: 32,0s, variación: +0,0s]
[15:12:49] [Server thread/INFO] (ai_villagers) Pescador Pescador FALLÓ pescando en BlockPos{x=-1, y=-60, z=-3} (fallos consecutivos: 2, éxitos acumulados: 2, tiempo total: 19.6s)
[15:12:49] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-1, y=-60, z=-3}
[15:12:49] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 26,0s, experiencia: 28,0s, variación: +4,0s]
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=4, y=-59, z=-1} en radios 1-16 (adaptativo hasta 24)
[15:13:04] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:04] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 3 bloques de agua superficial (total: 3)
[15:13:04] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:13:04] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:13:04] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:13:04] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:13:04] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=1, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=1, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=0, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:04] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:04] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:04] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:13:04] [Server thread/INFO] (ai_villagers) No se encontraron spots fallidos, buscando cualquier spot ignorando reservas...
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:04] [Server thread/INFO] (ai_villagers) Cooldown natural: 546 ticks (27,3s) [base: 29,0s, experiencia: 32,0s, variación: -4,7s]
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-6, y=-60, z=1} en radios 1-16 (adaptativo hasta 24)
[15:13:05] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:05] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:13:05] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 3 bloques de agua superficial (total: 3)
[15:13:05] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:13:05] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:13:05] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:13:05] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:13:05] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-1, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-1, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=-1, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=-1, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=-1, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:05] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:05] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:05] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:13:05] [Server thread/INFO] (ai_villagers) No se encontraron spots fallidos, buscando cualquier spot ignorando reservas...
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=0} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=0, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=-3} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=1, y=-60, z=1} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Pescador Pescador rechazó spot desesperado BlockPos{x=-2, y=-60, z=-2} - posición ocupada por bloque: Block{minecraft:grass_block}
[15:13:05] [Server thread/INFO] (ai_villagers) Cooldown natural: 520 ticks (26,0s) [base: 26,0s, experiencia: 28,0s, variación: -2,0s]
[15:13:17] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=4, y=-60, z=-2} en radios 1-16 (adaptativo hasta 24)
[15:13:17] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:17] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 3 bloques de agua superficial (total: 3)
[15:13:17] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:13:17] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:13:17] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:13:17] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:13:17] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=1, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=1, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=0, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:17] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:17] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:17] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:13:17] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=1, y=-60, z=1} a nivel de agua: -60
[15:13:17] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=1, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[15:13:17] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=1, y=-60, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-6, y=-60, z=0} en radios 1-16 (adaptativo hasta 24)
[15:13:18] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:18] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:13:18] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 3 bloques de agua superficial (total: 3)
[15:13:18] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:13:18] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:13:18] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:13:18] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:13:18] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-1, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-1, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=-1, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=-1, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=-1, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:18] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:18] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:13:18] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:13:18] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=-1, y=-60, z=-3} a nivel de agua: -60
[15:13:18] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-1, y=-60, z=-3} (se liberará automáticamente en 20 segundos)
[15:13:18] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-1, y=-60, z=-3}
[15:13:18] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=1, y=-61, z=0} - muy cerca (distancia: 1.7320508075688772)
[15:13:18] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (1,50, -60,90, 0,50) desde posición: (3,00, -60,00, 1,93)
[15:13:18] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=1, y=-61, z=0} (resultado: FALLO, evento: 13.6s)
[15:13:18] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=1, y=-60, z=1} hacia agua: Mutable{x=1, y=-61, z=0} (resultado: FALLO, evento en: 13.6s)
[15:13:18] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=1, y=-60, z=1} y comenzó a pescar inmediatamente
[15:13:19] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=-2} - muy cerca (distancia: 1.7320508075688772)
[15:13:19] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, -1,50) desde posición: (-1,94, -60,00, -3,00)
[15:13:19] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=-2} (resultado: FALLO, evento: 14.25s)
[15:13:19] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-1, y=-60, z=-3} hacia agua: Mutable{x=-1, y=-61, z=-2} (resultado: FALLO, evento en: 14.25s)
[15:13:19] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-1, y=-60, z=-3} y comenzó a pescar inmediatamente
[15:13:25] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca FALLIDA en 13.6s
[15:13:26] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca FALLIDA en 14.25s
[15:13:28] [Server thread/INFO] (ai_villagers) Pescador Pescador FALLÓ pescando en BlockPos{x=1, y=-60, z=1} (fallos consecutivos: 5, éxitos acumulados: 1, tiempo total: 18.6s)
[15:13:28] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=1, y=-60, z=1}
[15:13:28] [Server thread/INFO] (ai_villagers) Cooldown natural: 609 ticks (30,5s) [base: 29,0s, experiencia: 32,0s, variación: -1,6s]
[15:13:29] [Server thread/INFO] (ai_villagers) Pescador Pescador FALLÓ pescando en BlockPos{x=-1, y=-60, z=-3} (fallos consecutivos: 3, éxitos acumulados: 2, tiempo total: 19.25s)
[15:13:29] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-1, y=-60, z=-3}
[15:13:29] [Server thread/INFO] (ai_villagers) Cooldown natural: 526 ticks (26,3s) [base: 32,0s, experiencia: 32,0s, variación: -5,7s]
[15:13:31] [Render thread/INFO] (Minecraft) [System] [CHAT] Se ha establecido el punto de reaparición
[15:13:42] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:42] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-6, y=-59, z=-1} en radios 1-16 (adaptativo hasta 24)
[15:13:42] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:42] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:13:42] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 3 bloques de agua superficial (total: 3)
[15:13:42] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:13:42] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:13:42] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:13:42] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:13:42] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-1, y=-61, z=-1}
[15:13:42] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-1, y=-61, z=0}
[15:13:42] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=-1, y=-61, z=-2}
[15:13:42] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=-1, y=-61, z=-1}
[15:13:42] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=-1, y=-61, z=0}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:42] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:13:42] [Server thread/INFO] (ai_villagers) Encontrado spot de pesca: BlockPos{x=-1, y=-60, z=1} a nivel de agua: -60
[15:13:42] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-1, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[15:13:42] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-1, y=-60, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=0} - muy cerca (distancia: 1.7320508075688772)
[15:13:43] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, 0,50) desde posición: (-1,30, -60,00, 1,99)
[15:13:43] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=0} (resultado: FALLO, evento: 14.7s)
[15:13:43] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-1, y=-60, z=1} hacia agua: Mutable{x=-1, y=-61, z=0} (resultado: FALLO, evento en: 14.7s)
[15:13:43] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-1, y=-60, z=1} y comenzó a pescar inmediatamente
[15:13:43] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=6, y=-60, z=3} en radios 1-16 (adaptativo hasta 24)
[15:13:43] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:43] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:13:43] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 3 bloques de agua superficial (total: 3)
[15:13:43] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:13:43] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:13:43] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:13:43] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:13:43] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=1, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=1, y=-61, z=-2}
[15:13:43] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=0, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:43] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:43] [Server thread/INFO] (ai_villagers) Encontrado spot de pesca: BlockPos{x=-1, y=-60, z=1} a nivel de agua: -60
[15:13:43] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-1, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[15:13:43] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-1, y=-60, z=1}
[15:13:44] [Server thread/WARN] (ai_villagers) Pescador Pescador - Bobber no está en agua después de 2s (posición: BlockPos{x=-2, y=-60, z=0}, bloque: Block{minecraft:air}, bloque abajo: Block{minecraft:grass_block}), terminando pesca
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador - Pesca fallida por: bobber no en agua
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador - Spot BlockPos{x=-1, y=-60, z=1} marcado como fallido
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-1, y=-60, z=1}
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador - Cooldown fijo aplicado: 100 ticks por fallo de bobber (bobber no en agua)
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=0} - muy cerca (distancia: 1.4142135623730951)
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, 0,50) desde posición: (-0,02, -60,00, 1,91)
[15:13:44] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=0} (resultado: ÉXITO, evento: 16.55s)
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-1, y=-60, z=1} hacia agua: Mutable{x=-1, y=-61, z=0} (resultado: ÉXITO, evento en: 16.55s)
[15:13:44] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-1, y=-60, z=1} y comenzó a pescar inmediatamente
[15:13:45] [Server thread/WARN] (ai_villagers) Pescador Pescador - Bobber no está en agua después de 2s (posición: BlockPos{x=-1, y=-60, z=1}, bloque: Block{minecraft:air}, bloque abajo: Block{minecraft:grass_block}), terminando pesca
[15:13:45] [Server thread/INFO] (ai_villagers) Pescador Pescador - Pesca fallida por: bobber no en agua
[15:13:45] [Server thread/INFO] (ai_villagers) Pescador Pescador - Spot BlockPos{x=-1, y=-60, z=1} marcado como fallido
[15:13:45] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-1, y=-60, z=1}
[15:13:45] [Server thread/INFO] (ai_villagers) Pescador Pescador - Cooldown fijo aplicado: 100 ticks por fallo de bobber (bobber no en agua)
[15:13:46] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-5, y=-60, z=4} en radios 1-16 (adaptativo hasta 24)
[15:13:46] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:46] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:13:46] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 4 bloques de agua superficial (total: 4)
[15:13:46] [Server thread/INFO] (ai_villagers) Poca agua encontrada (4), extendiendo búsqueda hasta radio 24
[15:13:46] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 13)
[15:13:46] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (13), deteniendo
[15:13:46] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 13
[15:13:46] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-1, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-1, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=0, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=0, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=-1, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:46] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:46] [Server thread/INFO] (ai_villagers) Encontrado spot de pesca: BlockPos{x=-2, y=-60, z=-2} a nivel de agua: -60
[15:13:46] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-2, y=-60, z=-2} (se liberará automáticamente en 20 segundos)
[15:13:46] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-2, y=-60, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=-2} - muy cerca (distancia: 1.7320508075688772)
[15:13:48] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, -1,50) desde posición: (-1,93, -60,00, -0,98)
[15:13:48] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=-2} (resultado: ÉXITO, evento: 17.45s)
[15:13:48] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-2, y=-60, z=-2} hacia agua: Mutable{x=-1, y=-61, z=-2} (resultado: ÉXITO, evento en: 17.45s)
[15:13:48] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-2, y=-60, z=-2} y comenzó a pescar inmediatamente
[15:13:48] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=4, y=-59, z=-2} en radios 1-16 (adaptativo hasta 24)
[15:13:48] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:13:48] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 3 bloques de agua superficial (total: 3)
[15:13:48] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:13:48] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:13:48] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:13:48] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:13:48] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=1, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=1, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=0, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:dirt} en posición BlockPos{x=1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:13:48] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:13:48] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:13:48] [Server thread/INFO] (ai_villagers) Encontrado spot de pesca: BlockPos{x=-2, y=-60, z=-2} a nivel de agua: -60
[15:13:48] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-2, y=-60, z=-2} (se liberará automáticamente en 20 segundos)
[15:13:48] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-2, y=-60, z=-2}
[15:13:50] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=-2} - muy cerca (distancia: 1.7320508075688772)
[15:13:50] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, -1,50) desde posición: (-1,98, -60,00, -2,92)
[15:13:50] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=-2} (resultado: FALLO, evento: 14.65s)
[15:13:50] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-2, y=-60, z=-2} hacia agua: Mutable{x=-1, y=-61, z=-2} (resultado: FALLO, evento en: 14.65s)
[15:13:50] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-2, y=-60, z=-2} y comenzó a pescar inmediatamente
[15:13:56] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca EXITOSA en 17.45s
[15:13:57] [Server thread/INFO] (ai_villagers) Pescador Pescador tuvo ÉXITO pescando en BlockPos{x=-2, y=-60, z=-2} (éxitos consecutivos: 1, tiempo total: 18.45s)
[15:13:57] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-2, y=-60, z=-2}
[15:13:57] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 32,0s, experiencia: 31,0s, variación: +1,0s]
[15:13:57] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca FALLIDA en 14.65s
[15:13:59] [Server thread/INFO] (ai_villagers) Pescador Pescador FALLÓ pescando en BlockPos{x=-2, y=-60, z=-2} (fallos consecutivos: 7, éxitos acumulados: 0, tiempo total: 19.65s)
[15:13:59] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-2, y=-60, z=-2}
[15:13:59] [Server thread/INFO] (ai_villagers) Cooldown natural: 418 ticks (20,9s) [base: 32,0s, experiencia: 32,0s, variación: -11,1s]
[15:14:10] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=4, y=-59, z=-1} en radios 1-16 (adaptativo hasta 24)
[15:14:10] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:14:10] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 3 bloques de agua superficial (total: 3)
[15:14:10] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:14:10] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:14:10] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:14:10] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:14:10] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=1, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=1, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=0, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:dirt} en posición BlockPos{x=2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:10] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:14:10] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=1, y=-60, z=1} a nivel de agua: -60
[15:14:10] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=1, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[15:14:10] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=1, y=-60, z=1}
[15:14:11] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=1, y=-61, z=0} - muy cerca (distancia: 1.7320508075688772)
[15:14:11] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (1,50, -60,90, 0,50) desde posición: (2,97, -60,00, 1,88)
[15:14:11] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=1, y=-61, z=0} (resultado: FALLO, evento: 14.4s)
[15:14:11] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=1, y=-60, z=1} hacia agua: Mutable{x=1, y=-61, z=0} (resultado: FALLO, evento en: 14.4s)
[15:14:11] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=1, y=-60, z=1} y comenzó a pescar inmediatamente
[15:14:13] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-6, y=-59, z=-1} en radios 1-16 (adaptativo hasta 24)
[15:14:13] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:14:13] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:14:13] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 3 bloques de agua superficial (total: 3)
[15:14:13] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:14:13] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:14:13] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:14:13] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:14:13] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-1, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-1, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=-1, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=-1, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=-1, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:13] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:13] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:13] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:14:13] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=-1, y=-60, z=1} a nivel de agua: -60
[15:14:13] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-1, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[15:14:13] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-1, y=-60, z=1}
[15:14:14] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=0} - muy cerca (distancia: 1.7320508075688772)
[15:14:14] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, 0,50) desde posición: (-1,34, -60,00, 1,96)
[15:14:14] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=0} (resultado: ÉXITO, evento: 15.35s)
[15:14:14] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-1, y=-60, z=1} hacia agua: Mutable{x=-1, y=-61, z=0} (resultado: ÉXITO, evento en: 15.35s)
[15:14:14] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-1, y=-60, z=1} y comenzó a pescar inmediatamente
[15:14:18] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca FALLIDA en 14.4s
[15:14:20] [Server thread/INFO] (ai_villagers) Pescador Pescador FALLÓ pescando en BlockPos{x=1, y=-60, z=1} (fallos consecutivos: 8, éxitos acumulados: 0, tiempo total: 19.4s)
[15:14:20] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=1, y=-60, z=1}
[15:14:20] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 26,0s, experiencia: 32,0s, variación: +0,0s]
[15:14:22] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca EXITOSA en 15.35s
[15:14:22] [Server thread/INFO] (ai_villagers) Pescador Pescador tuvo ÉXITO pescando en BlockPos{x=-1, y=-60, z=1} (éxitos consecutivos: 2, tiempo total: 16.35s)
[15:14:22] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-1, y=-60, z=1}
[15:14:22] [Server thread/INFO] (ai_villagers) Cooldown natural: 460 ticks (23,0s) [base: 29,0s, experiencia: 27,0s, variación: -4,0s]
[15:14:34] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-8, y=-60, z=0} en radios 1-16 (adaptativo hasta 24)
[15:14:34] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:14:34] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 0 bloques de agua superficial (total: 0)
[15:14:34] [Server thread/INFO] (ai_villagers) Radio 5: encontrados 0 bloques de agua superficial (total: 0)
[15:14:34] [Server thread/INFO] (ai_villagers) Radio 7: encontrados 3 bloques de agua superficial (total: 3)
[15:14:34] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:14:34] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:14:34] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:14:34] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:14:34] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-1, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-1, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=-1, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=-1, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=-1, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:34] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:34] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:34] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:14:34] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=-1, y=-60, z=1} a nivel de agua: -60
[15:14:34] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-1, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[15:14:34] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-1, y=-60, z=1}
[15:14:34] [Server thread/INFO] (Minecraft) Saving and pausing game...
[15:14:34] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:overworld
[15:14:34] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_nether
[15:14:34] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_end
[15:14:40] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-1, y=-61, z=0} - muy cerca (distancia: 1.7320508075688772)
[15:14:40] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, 0,50) desde posición: (-1,31, -60,00, 1,98)
[15:14:40] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-1, y=-61, z=0} (resultado: ÉXITO, evento: 15.1s)
[15:14:40] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-1, y=-60, z=1} hacia agua: Mutable{x=-1, y=-61, z=0} (resultado: ÉXITO, evento en: 15.1s)
[15:14:40] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-1, y=-60, z=1} y comenzó a pescar inmediatamente
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=4, y=-60, z=-2} en radios 1-16 (adaptativo hasta 24)
[15:14:41] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:14:41] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 3 bloques de agua superficial (total: 3)
[15:14:41] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:14:41] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 9 bloques de agua superficial (total: 12)
[15:14:41] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (12), deteniendo
[15:14:41] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 12
[15:14:41] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=1, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=1, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=0, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-2} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-1} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-2} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:dirt}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-2} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-2} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=-1} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=0} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 3) para agua BlockPos{x=1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=-1} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:dirt}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-1} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=-1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=-1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=0, y=-61, z=0} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=0, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=0, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=0, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 2) para agua BlockPos{x=0, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-1, y=-61, z=0} para generar spots de pesca
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 3: Block{minecraft:dirt} en posición BlockPos{x=-1, y=-61, z=-3}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=-3} (dirección north, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:dirt}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=-3} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=0} (dirección east, distancia 3) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-1, y=-61, z=1}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-1, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-1, y=-60, z=1} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-2, y=-61, z=0}
[15:14:41] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-2, y=-60, z=0} (dirección west, distancia 1) para agua BlockPos{x=-1, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:14:41] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-2, y=-60, z=0} RECHAZADO
[15:14:41] [Server thread/INFO] (ai_villagers) No se encontraron spots válidos, buscando spots fallidos como último recurso...
[15:14:41] [Server thread/INFO] (ai_villagers) Usando spot fallido como último recurso: BlockPos{x=2, y=-60, z=-2} a nivel de agua: -60
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=2, y=-60, z=-2} (se liberará automáticamente en 20 segundos)
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=2, y=-60, z=-2}
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró agua en paso 1 (BlockPos{x=0, y=-61, z=-2}): Block{minecraft:water} - continuando
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró agua en paso 2 (BlockPos{x=-1, y=-61, z=-2}): Block{minecraft:water} - continuando
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-0,50, -60,90, -1,50) desde posición: (3,91, -60,00, -1,68)
[15:14:41] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia BlockPos{x=-1, y=-61, z=-2} (resultado: FALLO, evento: 13.85s)
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=2, y=-60, z=-2} hacia agua: BlockPos{x=-1, y=-61, z=-2} (resultado: FALLO, evento en: 13.85s)
[15:14:41] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=2, y=-60, z=-2} y comenzó a pescar inmediatamente
[15:14:47] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca EXITOSA en 15.1s
[15:14:48] [Server thread/INFO] (ai_villagers) Pescador Pescador tuvo ÉXITO pescando en BlockPos{x=-1, y=-60, z=1} (éxitos consecutivos: 3, tiempo total: 16.1s)
[15:14:48] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-1, y=-60, z=1}
[15:14:48] [Server thread/INFO] (ai_villagers) Cooldown natural: 612 ticks (30,6s) [base: 26,0s, experiencia: 23,0s, variación: +7,6s]
[15:14:48] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca FALLIDA en 13.85s
[15:14:51] [Server thread/INFO] (ai_villagers) Pescador Pescador FALLÓ pescando en BlockPos{x=2, y=-60, z=-2} (fallos consecutivos: 9, éxitos acumulados: 0, tiempo total: 18.85s)
[15:14:51] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=2, y=-60, z=-2}
[15:14:51] [Server thread/INFO] (ai_villagers) Cooldown natural: 640 ticks (32,0s) [base: 29,0s, experiencia: 32,0s, variación: +0,0s]
[15:15:03] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:15:03] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=-6, y=-60, z=-2} en radios 1-16 (adaptativo hasta 24)
[15:15:03] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:15:03] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 2 bloques de agua superficial (total: 2)
[15:15:03] [Server thread/INFO] (ai_villagers) Poca agua encontrada (2), extendiendo búsqueda hasta radio 24
[15:15:03] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 21 bloques de agua superficial (total: 23)
[15:15:03] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (23), deteniendo
[15:15:03] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 23
[15:15:03] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=-3, y=-61, z=-1}
[15:15:03] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=-3, y=-61, z=0}
[15:15:03] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=-3, y=-61, z=-1}
[15:15:03] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=-2, y=-61, z=-2}
[15:15:03] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=-3, y=-61, z=0}
[15:15:03] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-3, y=-61, z=-1} para generar spots de pesca
[15:15:03] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-3, y=-61, z=-2}
[15:15:03] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-3, y=-60, z=-2} (dirección north, distancia 1) para agua BlockPos{x=-3, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-3, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) No se encontró bloque sólido en dirección east hasta distancia 5
[15:15:03] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-3, y=-61, z=1}
[15:15:03] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-3, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=-3, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-3, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-4, y=-61, z=-1}
[15:15:03] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-4, y=-60, z=-1} (dirección west, distancia 1) para agua BlockPos{x=-3, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-4, y=-60, z=-1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=-3, y=-61, z=0} para generar spots de pesca
[15:15:03] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=-3, y=-61, z=-2}
[15:15:03] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-3, y=-60, z=-2} (dirección north, distancia 2) para agua BlockPos{x=-3, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-3, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) No se encontró bloque sólido en dirección east hasta distancia 5
[15:15:03] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=-3, y=-61, z=1}
[15:15:03] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-3, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=-3, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-3, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:03] [Server thread/INFO] (ai_villagers) Encontrado spot de pesca: BlockPos{x=-3, y=-60, z=-2} a nivel de agua: -60
[15:15:03] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=-3, y=-60, z=-2} (se liberará automáticamente en 20 segundos)
[15:15:03] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=-3, y=-60, z=-2}
[15:15:04] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=-3, y=-61, z=-1} - muy cerca (distancia: 1.7320508075688772)
[15:15:04] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (-2,50, -60,90, -0,50) desde posición: (-3,19, -60,00, -1,96)
[15:15:04] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=-3, y=-61, z=-1} (resultado: ÉXITO, evento: 17.3s)
[15:15:04] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=-3, y=-60, z=-2} hacia agua: Mutable{x=-3, y=-61, z=-1} (resultado: ÉXITO, evento en: 17.3s)
[15:15:04] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=-3, y=-60, z=-2} y comenzó a pescar inmediatamente
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:15:07] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=4, y=-59, z=-2} en radios 1-16 (adaptativo hasta 24)
[15:15:07] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:15:07] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 6 bloques de agua superficial (total: 6)
[15:15:07] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 6
[15:15:07] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=1, y=-61, z=-2}
[15:15:07] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=2, y=-61, z=-1}
[15:15:07] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=3, y=-61, z=0}
[15:15:07] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-1}
[15:15:07] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=2, y=-61, z=0}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=1, y=-61, z=-2} para generar spots de pesca
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=-3}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=-3} (dirección north, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=-3} RECHAZADO
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección east, distancia 1) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 3: Block{minecraft:grass_block} en posición BlockPos{x=1, y=-61, z=1}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=1, y=-60, z=1} (dirección south, distancia 3) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=1, y=-60, z=1} RECHAZADO
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección west distancia 4: Block{minecraft:grass_block} en posición BlockPos{x=-3, y=-61, z=-2}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=-3, y=-60, z=-2} (dirección west, distancia 4) para agua BlockPos{x=1, y=-61, z=-2} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=-3, y=-60, z=-2} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=2, y=-61, z=-1} para generar spots de pesca
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección north, distancia 1) para agua BlockPos{x=2, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=3, y=-61, z=-1}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=3, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=2, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=3, y=-60, z=-1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=1}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=2, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) No se encontró bloque sólido en dirección west hasta distancia 5
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=3, y=-61, z=0} para generar spots de pesca
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=3, y=-61, z=-1}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=3, y=-60, z=-1} (dirección north, distancia 1) para agua BlockPos{x=3, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=3, y=-60, z=-1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=4, y=-61, z=0}
[15:15:07] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=4, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=3, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=4, y=-60, z=0} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:07] [Server thread/INFO] (ai_villagers) Encontrado spot de pesca: BlockPos{x=3, y=-60, z=-1} a nivel de agua: -60
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=3, y=-60, z=-1} (se liberará automáticamente en 20 segundos)
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=3, y=-60, z=-1}
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró agua en paso 1 (BlockPos{x=1, y=-61, z=-1}): Block{minecraft:water} - continuando
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró agua en paso 2 (BlockPos{x=1, y=-61, z=-1}): Block{minecraft:water} - continuando
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (1,50, -60,90, -0,50) desde posición: (3,92, -59,00, -0,57)
[15:15:07] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia BlockPos{x=1, y=-61, z=-1} (resultado: FALLO, evento: 13.25s)
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=3, y=-60, z=-1} hacia agua: BlockPos{x=1, y=-61, z=-1} (resultado: FALLO, evento en: 13.25s)
[15:15:07] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=3, y=-60, z=-1} y comenzó a pescar inmediatamente
[15:15:08] [Server thread/WARN] (ai_villagers) Pescador Pescador - Bobber no está en agua después de 2s (posición: BlockPos{x=-5, y=-60, z=-1}, bloque: Block{minecraft:air}, bloque abajo: Block{minecraft:grass_block}), terminando pesca
[15:15:08] [Server thread/INFO] (ai_villagers) Pescador Pescador - Pesca fallida por: bobber no en agua
[15:15:08] [Server thread/INFO] (ai_villagers) Pescador Pescador - Spot BlockPos{x=3, y=-60, z=-1} marcado como fallido
[15:15:08] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=3, y=-60, z=-1}
[15:15:08] [Server thread/INFO] (ai_villagers) Pescador Pescador - Cooldown fijo aplicado: 100 ticks por fallo de bobber (bobber no en agua)
[15:15:10] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando búsqueda de spot de pesca
[15:15:10] [Server thread/INFO] (ai_villagers) Buscando agua desde posición BlockPos{x=5, y=-60, z=-2} en radios 1-16 (adaptativo hasta 24)
[15:15:10] [Server thread/INFO] (ai_villagers) Radio 1: encontrados 0 bloques de agua superficial (total: 0)
[15:15:10] [Server thread/INFO] (ai_villagers) Radio 3: encontrados 3 bloques de agua superficial (total: 3)
[15:15:10] [Server thread/INFO] (ai_villagers) Poca agua encontrada (3), extendiendo búsqueda hasta radio 24
[15:15:10] [Server thread/INFO] (ai_villagers) Radio extendido 18: encontrados 26 bloques de agua superficial (total: 29)
[15:15:10] [Server thread/INFO] (ai_villagers) Suficiente agua encontrada en búsqueda extendida (29), deteniendo
[15:15:10] [Server thread/INFO] (ai_villagers) Total de posiciones de agua encontradas: 29
[15:15:10] [Server thread/INFO] (ai_villagers) Agua 1: BlockPos{x=2, y=-61, z=-1}
[15:15:10] [Server thread/INFO] (ai_villagers) Agua 2: BlockPos{x=3, y=-61, z=0}
[15:15:10] [Server thread/INFO] (ai_villagers) Agua 3: BlockPos{x=2, y=-61, z=0}
[15:15:10] [Server thread/INFO] (ai_villagers) Agua 4: BlockPos{x=1, y=-61, z=-2}
[15:15:10] [Server thread/INFO] (ai_villagers) Agua 5: BlockPos{x=2, y=-61, z=-1}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=2, y=-61, z=-1} para generar spots de pesca
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección north, distancia 1) para agua BlockPos{x=2, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=3, y=-61, z=-1}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=3, y=-60, z=-1} (dirección east, distancia 1) para agua BlockPos{x=2, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=3, y=-60, z=-1} RECHAZADO
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=1}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=1} (dirección south, distancia 2) para agua BlockPos{x=2, y=-61, z=-1} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) No se encontró bloque sólido en dirección west hasta distancia 5
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=3, y=-61, z=0} para generar spots de pesca
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=3, y=-61, z=-1}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=3, y=-60, z=-1} (dirección north, distancia 1) para agua BlockPos{x=3, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=3, y=-60, z=-1} RECHAZADO
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=4, y=-61, z=0}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=4, y=-60, z=0} (dirección east, distancia 1) para agua BlockPos{x=3, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=4, y=-60, z=0} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=3, y=-61, z=1}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=3, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=3, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=3, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) No se encontró bloque sólido en dirección west hasta distancia 5
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando agua en BlockPos{x=2, y=-61, z=0} para generar spots de pesca
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección north distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=-2}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=-2} (dirección north, distancia 2) para agua BlockPos{x=2, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=-2} RECHAZADO
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección east distancia 2: Block{minecraft:grass_block} en posición BlockPos{x=4, y=-61, z=0}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=4, y=-60, z=0} (dirección east, distancia 2) para agua BlockPos{x=2, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=4, y=-60, z=0} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado bloque sólido en dirección south distancia 1: Block{minecraft:grass_block} en posición BlockPos{x=2, y=-61, z=1}
[15:15:10] [Server thread/INFO] (ai_villagers) Evaluando candidato BlockPos{x=2, y=-60, z=1} (dirección south, distancia 1) para agua BlockPos{x=2, y=-61, z=0} - bloque sólido: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Candidato BlockPos{x=2, y=-60, z=1} ACEPTADO como spot válido - bloque sólido debajo: Block{minecraft:grass_block}
[15:15:10] [Server thread/INFO] (ai_villagers) Encontrado spot de pesca: BlockPos{x=2, y=-60, z=1} a nivel de agua: -60
[15:15:10] [Server thread/INFO] (ai_villagers) Pescador Pescador reservó exitosamente spot BlockPos{x=2, y=-60, z=1} (se liberará automáticamente en 20 segundos)
[15:15:10] [Server thread/INFO] (ai_villagers) Pescador Pescador encontró nuevo spot de pesca: BlockPos{x=2, y=-60, z=1}
[15:15:12] [Server thread/INFO] (ai_villagers) Pescador Pescador usando agua original Mutable{x=2, y=-61, z=0} - muy cerca (distancia: 1.7320508075688772)
[15:15:12] [Server thread/INFO] (ai_villagers) Pescador Pescador orientado hacia target exacto: (2,50, -60,90, 0,50) desde posición: (3,05, -60,00, 1,97)
[15:15:12] [Server thread/INFO] (ai_villagers) Bobber personalizado creado para aldeano Pescador hacia Mutable{x=2, y=-61, z=0} (resultado: FALLO, evento: 13.95s)
[15:15:12] [Server thread/INFO] (ai_villagers) Pescador Pescador comenzó secuencia de pesca en posición: BlockPos{x=2, y=-60, z=1} hacia agua: Mutable{x=2, y=-61, z=0} (resultado: FALLO, evento en: 13.95s)
[15:15:12] [Server thread/INFO] (ai_villagers) Pescador Pescador llegó al spot BlockPos{x=2, y=-60, z=1} y comenzó a pescar inmediatamente
[15:15:12] [Server thread/INFO] (ai_villagers) Pescador Pescador - Iniciando evento de pesca EXITOSA en 17.3s
[15:15:13] [Server thread/INFO] (ai_villagers) Pescador Pescador tuvo ÉXITO pescando en BlockPos{x=-3, y=-60, z=-2} (éxitos consecutivos: 4, tiempo total: 18.3s)
[15:15:13] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=-3, y=-60, z=-2}
[15:15:13] [Server thread/INFO] (ai_villagers) Cooldown natural: 400 ticks (20,0s) [base: 29,0s, experiencia: 25,0s, variación: -5,0s]
[15:15:17] [Server thread/INFO] (ai_villagers) Pescador Pescador verificó items antes de ir al barril - NO tiene items
[15:15:17] [Server thread/INFO] (ai_villagers) Pescador Pescador terminando pesca por tiempo nocturno/sueño
[15:15:17] [Server thread/INFO] (ai_villagers) Pescador Pescador liberó spot BlockPos{x=2, y=-60, z=1}
[15:15:17] [Server thread/INFO] (ai_villagers) Pescador Pescador verificó items antes de ir al barril - NO tiene items
[15:15:27] [Server thread/INFO] (ai_villagers) Pescador Pescador verificó items antes de ir al barril - SÍ tiene items
[15:15:27] [Server thread/INFO] (ai_villagers) Pescador Pescador iniciando trabajo en barril: BlockPos{x=-6, y=-60, z=-1}
[15:15:27] [Server thread/INFO] (ai_villagers) Pescador iniciando almacenamiento en barril: BlockPos{x=-6, y=-60, z=-1}
[15:15:28] [Server thread/INFO] (ai_villagers) Pescador completó almacenamiento en barril
[15:16:31] [Server thread/INFO] (Minecraft) Saving and pausing game...
[15:16:31] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:overworld
[15:16:31] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_nether
[15:16:31] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_end
[15:16:32] [Render thread/INFO] (Minecraft) Stopping!
[15:16:32] [Server thread/INFO] (Minecraft) Player441 lost connection: Desconectado
[15:16:32] [Server thread/INFO] (Minecraft) Player441 ha abandonado la partida
[15:16:32] [Server thread/INFO] (Minecraft) Stopping singleplayer server as player logged out
[15:16:32] [Server thread/INFO] (Minecraft) Stopping server
[15:16:32] [Server thread/INFO] (Minecraft) Saving players
[15:16:32] [Server thread/INFO] (Minecraft) Saving worlds
[15:16:32] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:overworld
[15:16:32] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_nether
[15:16:32] [Server thread/INFO] (Minecraft) Saving chunks for level 'ServerLevel[Mundo nuevo]'/minecraft:the_end
[15:16:32] [Server thread/INFO] (Minecraft) ThreadedAnvilChunkStorage (Mundo nuevo (9)): All chunks are saved
[15:16:32] [Server thread/INFO] (Minecraft) ThreadedAnvilChunkStorage (DIM-1): All chunks are saved
[15:16:32] [Server thread/INFO] (Minecraft) ThreadedAnvilChunkStorage (DIM1): All chunks are saved
[15:16:32] [Server thread/INFO] (Minecraft) ThreadedAnvilChunkStorage: All dimensions are saved

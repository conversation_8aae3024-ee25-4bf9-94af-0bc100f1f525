buildscript {
	repositories {
		mavenCentral()
		maven { url = uri("https://maven.fabricmc.net/") }
	}
}

plugins {
	id 'fabric-loom' version '1.11.4'
	id 'maven-publish'
	id 'idea'
}

version = mod_version
group = maven_group

base {
	archivesName = project.archives_base_name
}

repositories {
	maven { url 'https://maven.fabricmc.net/' }
	mavenCentral()
}

loom {
	splitEnvironmentSourceSets()

	decompilers {
		fernflower {}
	}

	mods {
		create("AiVillagers") {
			sourceSet sourceSets.main
			sourceSet sourceSets.client
		}
	}
}

dependencies {
	minecraft "com.mojang:minecraft:${project.minecraft_version}"
	mappings  "net.fabricmc:yarn:${project.yarn_mappings}:v2"
	modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"
	modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"
}

processResources {
	inputs.property("version", version)
	inputs.property("minecraft_version", project.minecraft_version)
	inputs.property("loader_version", project.loader_version)

	filesMatching("fabric.mod.json") {
		expand([
			"version": version,
			"minecraft_version": project.minecraft_version,
			"loader_version": project.loader_version
		])
	}

	// Exclude development files
	exclude "**/*.xcf"
	exclude "**/*.psd"
	exclude "**/*.md"
}

tasks.withType(JavaCompile).configureEach {
	it.options.release = 23
	it.options.encoding = "UTF-8"
	it.options.compilerArgs += ["-Xlint:deprecation", "-Xlint:unchecked", "-Xlint:rawtypes"]
}

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(23)
	}
	withSourcesJar()
}

jar {
	from("LICENSE") {
		rename { "${it}_${project.base.archivesName.get()}" }
	}
}

publishing {
	publications {
		create("mavenJava", MavenPublication) {
			artifactId = project.archives_base_name
			from components.java
		}
	}
	repositories {
	}
}

// IntelliJ IDEA configuration
idea {
	module {
		downloadJavadoc = true
		downloadSources = true

		// Exclude build directories from indexing
		excludeDirs += file('build')
		excludeDirs += file('run')
		excludeDirs += file('.gradle')
	}
}